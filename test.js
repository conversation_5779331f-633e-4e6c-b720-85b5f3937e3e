import { promises as fs } from 'fs';

async function saveFile(url) {
  let apiKey = '9fb87bd7-0aff-4b43-94f4-cf47d3c49b15'
  const response = await fetch(url, {
    headers: {
      Authorization: `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to download file');
  }

  const arrayBuffer = await response.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);
  await fs.writeFile('test.mp4', buffer);
  console.log(`File saved to ${savePath}`);
}

await saveFile('https://pixeldrain.com/api/file/r7WuzWza');