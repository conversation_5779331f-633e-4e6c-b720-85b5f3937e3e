import { config } from 'dotenv';
config();
import fs from 'fs/promises';

import { SlashCommandBuilder } from '@discordjs/builders';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ADMIN_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export const data = new SlashCommandBuilder()
    .setName('createseasonalroles')
    .setDescription('Creates roles for seasonal anime and generates a role mapping JSON file');

export async function run({ interaction, client, handler }) {
    // User authentication
    if (interaction.user.id !== '351006685587963916' && interaction.user.id !== '185126303677153281') {
        await interaction.reply({
            content: '<PERSON><PERSON> mo<PERSON> tej komendy.',
            ephemeral: true,
        });
        return;
    }

    await interaction.deferReply();
    await interaction.editReply('Fetching seasonal anime from database...');

    try {
        //CHANGE: season and year here as season changes.
        const { data: animeList, error } = await supabase
            .from('anime_metadata')
            .select('anilist_id, romaji_title, synonyms')
            .eq('season', 'SPRING')
            .eq('season_year', 2025);

        if (error) {
            throw new Error(`Supabase query error: ${error.message}`);
        }

        if (!animeList || animeList.length === 0) {
            await interaction.editReply('No seasonal anime found in the database.');
            return;
        }

        await interaction.editReply(`Found ${animeList.length} seasonal anime. Creating roles...`);

        const roleMappings = {};
        let createdCount = 0;
        let skippedCount = 0;

        // Process each anime and create roles
        for (const anime of animeList) {
            try {
                // Determine the role name (prioritize synonyms if available)
                let roleName = '';

                if (anime.synonyms && anime.synonyms.length > 0) {
                    // Find the shortest synonym that's not too short
                    const validSynonyms = anime.synonyms.filter(s => s && s.length >= 3 && s.length <= 100);
                    if (validSynonyms.length > 0) {
                        roleName = validSynonyms.reduce((shortest, current) =>
                            (current.length < shortest.length) ? current : shortest
                        );
                    }
                }

                // If no valid synonym was found, use romaji_title
                if (!roleName) {
                    roleName = anime.romaji_title;

                    // Truncate long role names to fit Discord's 100-character limit
                    if (roleName.length > 100) {
                        roleName = roleName.substring(0, 97) + '...';
                    }
                }

                // Check if role already exists
                const existingRole = interaction.guild.roles.cache.find(role => role.name === roleName);
                if (existingRole) {
                    roleMappings[anime.anilist_id] = `<@&${existingRole.id}>`;
                    skippedCount++;
                    continue;
                }

                // Create the role
                const newRole = await interaction.guild.roles.create({
                    name: roleName,
                    mentionable: true,
                    reason: 'seasonal anime role'
                });
                // Add to mappings
                roleMappings[anime.anilist_id] = `<@&${newRole.id}>`;
                createdCount++;

                // Update progress occasionally
                if (createdCount % 5 === 0 || createdCount + skippedCount === animeList.length) {
                    await interaction.editReply(
                        `Processing seasonal anime roles... ` +
                        `\nProgress: ${createdCount + skippedCount}/${animeList.length} ` +
                        `\nCreated: ${createdCount} | Skipped: ${skippedCount}`
                    );
                }

                // Add a small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (roleError) {
                console.error(`Error creating role for ${anime.romaji_title}:`, roleError);
            }
        }

        // Save the role mappings to a JSON file
        await fs.writeFile(
            './_resources/seasonal_role_pings.json',
            JSON.stringify(roleMappings, null, 2)
        );

        await interaction.editReply(
            `Completed creating seasonal anime roles!\n` +
            `Total anime: ${animeList.length}\n` +
            `Created roles: ${createdCount}\n` +
            `Skipped (already exist): ${skippedCount}\n` +
            `Role mappings have been saved to '_resources/seasonal_role_pings.json'`
        );
    } catch (error) {
        console.error('Error processing seasonal anime roles:', error);
        await interaction.editReply(`An error occurred: ${error.message}`);
    }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
    devOnly: true,
    deleted: false,
};